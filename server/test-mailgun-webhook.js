#!/usr/bin/env node

/**
 * Test script for the Mailgun webhook handler
 * This simulates what Mailgun would send to our /handle-ticket-forwarding endpoint
 */

const fs = require('fs');
const path = require('path');

// Create a simple PDF buffer for testing (this is a minimal PDF structure)
const createTestPdfBuffer = () => {
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test PDF Content) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
297
%%EOF`;
  
  return Buffer.from(pdfContent);
};

// Test data that simulates a Mailgun webhook payload
const createTestPayload = () => {
  const pdfBuffer = createTestPdfBuffer();
  const base64Pdf = pdfBuffer.toString('base64');
  
  return {
    recipient: '<EMAIL>',
    sender: '<EMAIL>', 
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Support Request with PDF Attachment',
    'body-plain': 'Please find the attached PDF with my support request details.',
    'body-html': '<p>Please find the attached PDF with my support request details.</p>',
    'attachment-count': '1',
    'attachment-1': base64Pdf,
    'attachment-1-filename': 'support-request.pdf',
    timestamp: new Date().toISOString(),
    token: 'test-token-123',
    signature: 'test-signature-456',
    'message-headers': JSON.stringify([
      ['From', '<EMAIL>'],
      ['To', '<EMAIL>'],
      ['Subject', 'Support Request with PDF Attachment']
    ])
  };
};

// Test the endpoint
async function testWebhook() {
  const SERVER_URL = process.env.SERVER_URL || 'http://localhost:3000';
  const payload = createTestPayload();
  
  console.log('🧪 Testing Mailgun webhook handler...');
  console.log(`📡 Server URL: ${SERVER_URL}`);
  console.log(`📧 Test payload keys: ${Object.keys(payload).join(', ')}`);
  
  try {
    // Test with form data (typical Mailgun format)
    const formData = new URLSearchParams();
    Object.entries(payload).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    console.log('\n🚀 Sending test request...');
    const response = await fetch(`${SERVER_URL}/handle-ticket-forwarding`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mailgun/Test'
      },
      body: formData
    });
    
    const responseText = await response.text();
    console.log(`\n📊 Response Status: ${response.status}`);
    console.log(`📄 Response Headers:`, Object.fromEntries(response.headers.entries()));
    
    try {
      const responseJson = JSON.parse(responseText);
      console.log('\n✅ Response JSON:');
      console.log(JSON.stringify(responseJson, null, 2));
      
      if (responseJson.success) {
        console.log('\n🎉 Test PASSED!');
        console.log(`📧 Email extracted: ${responseJson.email.sender} → ${responseJson.email.recipient}`);
        console.log(`📎 PDF attachments found: ${responseJson.totalAttachments}`);
        
        if (responseJson.pdfAttachments.length > 0) {
          responseJson.pdfAttachments.forEach((pdf, index) => {
            console.log(`   📄 PDF ${index + 1}: ${pdf.filename} (${pdf.size} bytes)`);
            console.log(`   ✅ Parsed successfully: ${pdf.success}`);
            if (pdf.content) {
              console.log(`   📝 Content preview: "${pdf.content.substring(0, 100)}..."`);
            }
          });
        }
      } else {
        console.log('\n❌ Test FAILED!');
        console.log(`Error: ${responseJson.error}`);
      }
    } catch (parseError) {
      console.log('\n❌ Failed to parse response as JSON:');
      console.log(responseText);
    }
    
  } catch (error) {
    console.error('\n💥 Test failed with error:', error.message);
    
    // Try to test if server is running
    try {
      const healthResponse = await fetch(`${SERVER_URL}/health`);
      if (healthResponse.ok) {
        console.log('✅ Server is running (health check passed)');
      } else {
        console.log('❌ Server health check failed');
      }
    } catch (healthError) {
      console.log('❌ Cannot reach server - make sure it\'s running with: bun run dev');
    }
  }
}

// Run the test
if (require.main === module) {
  testWebhook().catch(console.error);
}

module.exports = { testWebhook, createTestPayload };
