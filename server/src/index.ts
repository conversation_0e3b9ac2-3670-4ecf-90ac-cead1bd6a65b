import { Hono } from "hono";
import { cors } from "hono/cors";
import type { ApiResponse } from "shared/dist";
// @ts-ignore - pdf-extraction doesn't have types
import extract from "pdf-extraction";

// Detailed logging utility
const logWithTimestamp = (
  level: "INFO" | "ERROR" | "WARN" | "DEBUG",
  message: string,
  data?: any
) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...(data && { data }),
  };
  console.log(JSON.stringify(logEntry));
};

const app = new Hono();

app.use(cors());

// Add request logging middleware
app.use("*", async (c, next) => {
  const start = Date.now();
  const method = c.req.method;
  const path = c.req.path;

  logWithTimestamp("INFO", "Incoming request", {
    method,
    path,
    userAgent: c.req.header("user-agent"),
    contentLength: c.req.header("content-length"),
    contentType: c.req.header("content-type"),
  });

  await next();

  const duration = Date.now() - start;
  logWithTimestamp("INFO", "Request completed", {
    method,
    path,
    duration,
    status: c.res.status,
  });
});

app.get("/", (c) => {
  return c.text("Hello Hono!");
});

app.get("/hello", async (c) => {
  const data: ApiResponse = {
    message: "Hello BHVR!",
    success: true,
  };

  return c.json(data, { status: 200 });
});

// Health check endpoint for debugging
app.get("/health", async (c) => {
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: "1.0.0",
    endpoints: {
      "/": "Basic hello endpoint",
      "/hello": "API test endpoint",
      "/handle-ticket-forwarding": "Mailgun webhook handler",
      "/health": "Health check endpoint",
    },
  };

  logWithTimestamp("INFO", "Health check requested", health);
  return c.json(health, { status: 200 });
});

// Types for email processing
interface EmailData {
  recipient: string;
  sender: string;
  from: string;
  to: string;
  subject: string;
  bodyPlain: string;
  bodyHtml: string;
  attachmentCount: number;
  timestamp: string;
  messageHeaders: string;
}

interface PdfAttachment {
  filename: string;
  size: number;
  buffer: Buffer;
}

// Extract email data from Mailgun webhook payload
const extractEmailData = (body: any): EmailData => {
  logWithTimestamp("DEBUG", "Extracting email data from body", {
    availableKeys: Object.keys(body).slice(0, 20), // Log first 20 keys to avoid huge logs
  });

  const emailData = {
    recipient: body.recipient || body.to || "unknown",
    sender: body.sender || body.from || "unknown",
    from: body.from || "unknown",
    to: body.to || body.recipient || "unknown",
    subject: body.subject || "No subject",
    bodyPlain: body["body-plain"] || body.bodyPlain || "",
    bodyHtml: body["body-html"] || body.bodyHtml || "",
    attachmentCount: parseInt(
      body["attachment-count"] || body.attachmentCount || "0"
    ),
    timestamp: body.timestamp || new Date().toISOString(),
    messageHeaders: body["message-headers"] || body.messageHeaders || "",
  };

  logWithTimestamp("DEBUG", "Extracted email data", emailData);
  return emailData;
};

// Extract PDF attachments from Mailgun webhook payload
const extractPdfAttachments = async (body: any): Promise<PdfAttachment[]> => {
  const attachments: PdfAttachment[] = [];

  logWithTimestamp("DEBUG", "Looking for PDF attachments", {
    attachmentCount: body["attachment-count"] || body.attachmentCount || 0,
    bodyKeys: Object.keys(body)
      .filter((key) => key.includes("attachment"))
      .slice(0, 10),
  });

  // Mailgun sends attachments as attachment-1, attachment-2, etc.
  const attachmentCount = parseInt(
    body["attachment-count"] || body.attachmentCount || "0"
  );

  for (let i = 1; i <= attachmentCount; i++) {
    const attachmentKey = `attachment-${i}`;
    const attachment = body[attachmentKey];

    if (attachment) {
      logWithTimestamp("DEBUG", `Found attachment ${i}`, {
        attachmentKey,
        hasAttachment: !!attachment,
        attachmentType: typeof attachment,
      });

      // Check if it's a PDF
      let filename = "";
      let buffer: Buffer | null = null;

      if (attachment instanceof File) {
        filename = attachment.name;
        if (filename.toLowerCase().endsWith(".pdf")) {
          const arrayBuffer = await attachment.arrayBuffer();
          buffer = Buffer.from(arrayBuffer);
        }
      } else if (typeof attachment === "object" && attachment.filename) {
        filename = attachment.filename;
        if (filename.toLowerCase().endsWith(".pdf") && attachment.content) {
          buffer = Buffer.from(attachment.content, "base64");
        }
      } else if (typeof attachment === "string") {
        // Sometimes attachments come as base64 strings
        const filenameKey = `attachment-${i}-filename`;
        filename = body[filenameKey] || `attachment-${i}.pdf`;
        if (filename.toLowerCase().endsWith(".pdf")) {
          buffer = Buffer.from(attachment, "base64");
        }
      }

      if (buffer && filename.toLowerCase().endsWith(".pdf")) {
        attachments.push({
          filename,
          size: buffer.length,
          buffer,
        });
        logWithTimestamp("INFO", `Added PDF attachment ${i}`, {
          filename,
          size: buffer.length,
        });
      } else {
        logWithTimestamp("DEBUG", `Skipped non-PDF attachment ${i}`, {
          filename,
          isPdf: filename.toLowerCase().endsWith(".pdf"),
          hasBuffer: !!buffer,
        });
      }
    }
  }

  return attachments;
};

// Parse PDF content using pdf-extraction
const parsePdfContent = async (buffer: Buffer): Promise<string> => {
  logWithTimestamp("DEBUG", "Starting PDF parsing", {
    bufferSize: buffer.length,
  });

  try {
    // Use pdf-extraction to extract text from the PDF buffer
    const data = await extract(buffer);

    logWithTimestamp("DEBUG", "PDF parsing completed", {
      textLength: data.text.length,
      numPages: data.pages?.length || 0,
      metadata: data.meta || {},
    });

    return data.text;
  } catch (error) {
    logWithTimestamp("ERROR", "PDF parsing failed", {
      error: error instanceof Error ? error.message : String(error),
      bufferSize: buffer.length,
    });
    throw error;
  }
};

app.post("/handle-ticket-forwarding", async (c) => {
  const startTime = Date.now();
  logWithTimestamp("INFO", "Received ticket forwarding request", {
    headers: c.req.header(),
    contentType: c.req.header("content-type"),
    method: c.req.method,
    url: c.req.url,
  });

  try {
    // Parse the request body based on content type
    let body: any;
    const contentType = c.req.header("content-type") || "";

    logWithTimestamp("DEBUG", "Parsing request body", { contentType });

    if (contentType.includes("application/json")) {
      body = await c.req.json();
      logWithTimestamp("DEBUG", "Parsed JSON body", {
        bodyKeys: Object.keys(body),
      });
    } else if (
      contentType.includes("application/x-www-form-urlencoded") ||
      contentType.includes("multipart/form-data")
    ) {
      // Mailgun typically sends form data
      body = await c.req.parseBody();
      logWithTimestamp("DEBUG", "Parsed form body", {
        bodyKeys: Object.keys(body),
      });
    } else {
      const text = await c.req.text();
      logWithTimestamp("DEBUG", "Parsed text body", {
        textLength: text.length,
      });
      body = { rawText: text };
    }

    // Extract email information
    const emailData = extractEmailData(body);
    logWithTimestamp("INFO", "Extracted email data", emailData);

    // Check for PDF attachments
    const pdfAttachments = await extractPdfAttachments(body);
    logWithTimestamp("INFO", "Found PDF attachments", {
      count: pdfAttachments.length,
    });

    // Process each PDF attachment
    const pdfResults = [];
    for (let i = 0; i < pdfAttachments.length; i++) {
      const attachment = pdfAttachments[i];
      if (!attachment) {
        logWithTimestamp("WARN", `Attachment ${i + 1} is undefined, skipping`);
        continue;
      }

      logWithTimestamp("INFO", `Processing PDF attachment ${i + 1}`, {
        filename: attachment.filename,
        size: attachment.size,
      });

      try {
        const pdfContent = await parsePdfContent(attachment.buffer);
        pdfResults.push({
          filename: attachment.filename,
          size: attachment.size,
          content: pdfContent,
          success: true,
        });
        logWithTimestamp("INFO", `Successfully parsed PDF ${i + 1}`, {
          filename: attachment.filename,
          contentLength: pdfContent.length,
        });
      } catch (pdfError) {
        logWithTimestamp("ERROR", `Failed to parse PDF ${i + 1}`, {
          filename: attachment.filename,
          error:
            pdfError instanceof Error ? pdfError.message : String(pdfError),
        });
        pdfResults.push({
          filename: attachment.filename,
          size: attachment.size,
          content: null,
          success: false,
          error:
            pdfError instanceof Error ? pdfError.message : String(pdfError),
        });
      }
    }

    const processingTime = Date.now() - startTime;
    const response = {
      success: true,
      processingTimeMs: processingTime,
      email: emailData,
      pdfAttachments: pdfResults,
      totalAttachments: pdfAttachments.length,
    };

    logWithTimestamp("INFO", "Successfully processed ticket forwarding", {
      processingTimeMs: processingTime,
      emailRecipient: emailData.recipient,
      emailSender: emailData.sender,
      pdfCount: pdfResults.length,
      successfulPdfs: pdfResults.filter((p) => p.success).length,
    });

    return c.json(response, { status: 200 });
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    logWithTimestamp("ERROR", "Failed to process ticket forwarding", {
      error: errorMessage,
      stack: errorStack,
      processingTimeMs: processingTime,
    });

    return c.json(
      {
        success: false,
        error: errorMessage,
        processingTimeMs: processingTime,
      },
      { status: 500 }
    );
  }
});

export default app;
